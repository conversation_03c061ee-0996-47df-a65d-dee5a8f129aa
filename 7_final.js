// 终极解决方案：用系统TTS替换百度TTS
console.log("[+] 开始TTS替换脚本...");

if (ObjC.available) {
    try {
        var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
        if (BDText2VoiceManager) {
            console.log("[+] 找到BDText2VoiceManager类");
            
            // 创建系统TTS管理器
            var systemSynthesizer = null;
            var speechRate = 0.8; // 默认语速，可以动态调整
            
            function initSystemTTS() {
                try {
                    var AVSpeechSynthesizer = ObjC.classes.AVSpeechSynthesizer;
                    if (AVSpeechSynthesizer) {
                        systemSynthesizer = AVSpeechSynthesizer.alloc().init();
                        console.log("[系统TTS] 初始化成功");
                        return true;
                    }
                } catch (e) {
                    console.log("[-] 系统TTS初始化失败: " + e);
                }
                return false;
            }
            
            function playWithSystemTTS(text) {
                try {
                    if (!systemSynthesizer) {
                        if (!initSystemTTS()) {
                            return false;
                        }
                    }
                    
                    var AVSpeechUtterance = ObjC.classes.AVSpeechUtterance;
                    if (AVSpeechUtterance) {
                        var utterance = AVSpeechUtterance.speechUtteranceWithString_(text);
                        
                        // 设置语音参数
                        utterance.setRate_(speechRate);  // 使用动态语速
                        utterance.setVolume_(1.0); // 音量
                        
                        systemSynthesizer.speakUtterance_(utterance);
                        console.log("[系统TTS] 播放: " + text);
                        return true;
                    }
                } catch (e) {
                    console.log("[-] 系统TTS播放失败: " + e);
                }
                return false;
            }
            
            // Hook百度TTS的playVoiceStr方法，替换为系统TTS
            var playVoiceMethod = BDText2VoiceManager['- playVoiceStr:continuePlay:'];
            if (playVoiceMethod) {
                Interceptor.attach(playVoiceMethod.implementation, {
                    onEnter: function(args) {
                        var text = ObjC.Object(args[2]);
                        var textStr = text.toString().trim();
                        
                        console.log("[替换] 拦截百度TTS播放: '" + textStr + "'");
                        
                        // 用系统TTS播放
                        if (playWithSystemTTS(textStr)) {
                            console.log("[替换] 已用系统TTS播放");
                        } else {
                            console.log("[替换] 系统TTS播放失败，继续百度TTS");
                        }
                    },
                    onLeave: function(retval) {
                        // 让原方法继续执行，但我们已经用系统TTS播放了
                        console.log("[替换] 百度TTS方法执行完成");
                    }
                });
                console.log("[+] 已hook百度TTS播放方法");
            }
            
            // 强制修复所有TTS相关状态
            function forceFixAllTTS() {
                try {
                    console.log("\n=== 强制修复所有TTS状态 ===");
                    
                    var shareInstance = BDText2VoiceManager.shareInstance();
                    if (shareInstance) {
                        // 修复百度TTS状态
                        shareInstance.setValue_forKey_(ObjC.classes.NSNumber.numberWithBool_(true), "_isConfigTtsSuccess");
                        shareInstance.setIsMute_(false);
                        console.log("[修复] 百度TTS状态已修复");
                    }
                    
                    // 设置音频会话
                    var AVAudioSession = ObjC.classes.AVAudioSession;
                    var sharedSession = AVAudioSession.sharedInstance();
                    var playbackCategory = ObjC.classes.NSString.stringWithString_("AVAudioSessionCategoryPlayback");
                    sharedSession.setCategory_(playbackCategory);
                    sharedSession.setActive_(true);
                    console.log("[修复] 音频会话已设置");
                    
                    // 初始化系统TTS
                    initSystemTTS();
                    
                    console.log("========================\n");
                    
                } catch (e) {
                    console.log("[-] 强制修复失败: " + e);
                }
            }
            
            // Hook语音播报检查函数，确保总是通过
            try {
                var baseAddress = Module.findBaseAddress("SkyCamera");
                if (baseAddress) {
                    var checkFunction = baseAddress.add(0x3C9930);
                    Interceptor.attach(checkFunction, {
                        onLeave: function(retval) {
                            var originalResult = retval.toInt32();
                            if (originalResult === 0) {
                                console.log("[强制] 检查函数返回0，强制改为1");
                                retval.replace(ptr(1));
                            }
                        }
                    });
                    console.log("[+] 已hook语音检查函数");
                }
            } catch (e) {
                console.log("[-] Hook检查函数失败: " + e);
            }
            
            // // 测试函数
            // function testBothTTS() {
            //     try {
            //         console.log("\n=== 测试TTS播放 ===");
                    
            //         // 测试系统TTS
            //         console.log("[测试] 测试系统TTS...");
            //         playWithSystemTTS("系统语音测试");
                    
            //         // 测试百度TTS（会被我们的hook拦截并用系统TTS播放）
            //         setTimeout(function() {
            //             console.log("[测试] 测试百度TTS（会被替换）...");
            //             var shareInstance = BDText2VoiceManager.shareInstance();
            //             if (shareInstance) {
            //                 var textStr = ObjC.classes.NSString.stringWithString_("百度语音测试");
            //                 shareInstance.playVoiceStr_continuePlay_(textStr, true);
            //             }
            //         }, 2000);
                    
            //         console.log("==================\n");
                    
            //     } catch (e) {
            //         console.log("[-] 测试失败: " + e);
            //     }
            // }
            
            // 执行修复和测试
            setTimeout(forceFixAllTTS, 1000);    // 1秒后修复
            setTimeout(testBothTTS, 3000);       // 3秒后测试
            
        } else {
            console.log("[-] 未找到BDText2VoiceManager类");
        }
        
    } catch (e) {
        console.log("[-] 脚本执行出错: " + e);
    }
    
} else {
    console.log("[-] Objective-C运行时不可用");
}

console.log("[+] TTS替换脚本加载完成！");
console.log("[提示] 当前语速: " + (typeof speechRate !== 'undefined' ? speechRate : 0.8));
console.log("[提示] 如需调整语速，可以在控制台执行:");
console.log("       speechRate = 0.6; (慢速)");
console.log("       speechRate = 0.8; (正常)");
console.log("       speechRate = 1.0; (快速)");
console.log("       speechRate = 1.2; (很快)");
