// Frida脚本 - Hook Keychain.set方法
// 目标地址: 0x116CD20 (去掉基址0x10后的地址)

console.log("[*] 开始Hook Keychain.set方法...");

// 获取模块基址
var moduleName = "SkyCamera"; // 替换为你的应用名称
var baseAddress = Module.findBaseAddress(moduleName);

if (baseAddress) {
    console.log("[+] 找到模块基址: " + baseAddress);
    
    // 计算目标函数地址
    var targetAddress = baseAddress.add(0x116CD20);
    console.log("[+] 目标函数地址: " + targetAddress);
    
    // Hook函数
    Interceptor.attach(targetAddress, {
        onEnter: function(args) {
            console.log("\n[*] ===== Keychain.set 被调用 =====");

            try {
                // Swift String结构: {countAndFlagsBits, object}
                // args[0] = value._countAndFlagsBits
                // args[1] = value._object
                // args[2] = key._countAndFlagsBits
                // args[3] = key._object
                // args[4] = ignoringAttributeSynchronizable

                // 正确的Swift字符串解析函数
                function parseSwiftString(countAndFlagsBits, objectPtr) {
                    try {
                        // 获取discriminator (最高4位 b63:b60)
                        var discriminator = (countAndFlagsBits.shr(60)).and(0xF).toNumber();
                        var isSmall = (discriminator & 0x2) !== 0; // b61 = isSmall

                        console.log("[DEBUG] discriminator: 0x" + discriminator.toString(16) + ", isSmall: " + isSmall);

                        if (isSmall) {
                            // Small String: 数据存储在这两个64位值中
                            // countAndFlagsBits包含前8字节数据
                            // objectPtr包含后7字节数据 + count信息

                            // 从objectPtr的最高字节获取count (byte15的低4位)
                            var byte15 = (objectPtr.shr(56)).and(0xFF).toNumber();
                            var count = byte15 & 0xF; // 低4位是count

                            console.log("[DEBUG] Small string count: " + count);

                            if (count > 15) {
                                return "[Small string count > 15, 可能解析错误]";
                            }

                            var bytes = [];

                            // 从countAndFlagsBits读取前8字节 (如果需要)
                            var firstPart = countAndFlagsBits;
                            for (var i = 0; i < Math.min(count, 8); i++) {
                                var byte = firstPart.and(0xFF).toNumber();
                                bytes.push(String.fromCharCode(byte));
                                firstPart = firstPart.shr(8);
                            }

                            // 从objectPtr读取剩余字节 (如果count > 8)
                            if (count > 8) {
                                var secondPart = objectPtr;
                                for (var i = 8; i < count; i++) {
                                    var byte = secondPart.and(0xFF).toNumber();
                                    bytes.push(String.fromCharCode(byte));
                                    secondPart = secondPart.shr(8);
                                }
                            }

                            return bytes.join('');

                        } else {
                            // Large String
                            // countAndFlagsBits = flag (包含count在低48位)
                            // objectPtr = discriminator + objectAddr

                            var count = countAndFlagsBits.and(ptr("0xFFFFFFFFFFFF")).toNumber(); // 低48位是count
                            var objectAddr = objectPtr.and(ptr("0x0FFFFFFFFFFFFFFF")); // 低60位是objectAddr

                            console.log("[DEBUG] Large string count: " + count + ", objectAddr: " + objectAddr);

                            if (count > 0 && count < 10000) {
                                // 检查是否是Native字符串
                                var isNative = (countAndFlagsBits.shr(61)).and(1).toNumber(); // b61 = native
                                var realStrAddr;

                                if (isNative) {
                                    // Native: 真正字符串地址 = objectAddr + 0x20
                                    realStrAddr = objectAddr.add(0x20);
                                } else {
                                    // Shared/Foreign: 真正字符串地址 = objectAddr
                                    realStrAddr = objectAddr;
                                }

                                console.log("[DEBUG] isNative: " + isNative + ", realStrAddr: " + realStrAddr);

                                try {
                                    return realStrAddr.readUtf8String(count);
                                } catch (e) {
                                    console.log("[DEBUG] 读取Large string失败: " + e.message);
                                    return "[Large string读取失败]";
                                }
                            }
                        }

                        return "[无法解析的字符串]";
                    } catch (e) {
                        console.log("[DEBUG] 解析字符串出错: " + e.message);
                        return "[解析出错: " + e.message + "]";
                    }
                }

                // 解析value字符串
                var valueString = parseSwiftString(args[0], args[1]);
                if (!valueString) {
                    valueString = "[无法解析value]";
                }

                // 解析key字符串
                var keyString = parseSwiftString(args[2], args[3]);
                if (!keyString) {
                    keyString = "[无法解析key]";
                }

                // 读取ignoringAttributeSynchronizable参数
                var ignoreSync = args[4].toInt32() ? "true" : "false";

                console.log("[+] Key: " + keyString);
                console.log("[+] Value: " + valueString);
                console.log("[+] IgnoreSync: " + ignoreSync);

                // 如果是bd_tts_secret_key，特别标记
                if (keyString.indexOf("bd_tts_secret_key") !== -1) {
                    console.log("[!] *** 检测到bd_tts_secret_key保存操作! ***");
                    console.log("[!] Secret Key值: " + valueString);
                }

                // 打印原始参数用于调试
                console.log("[DEBUG] 原始参数:");
                console.log("  args[0] (value.countAndFlagsBits): 0x" + args[0].toString(16));
                console.log("  args[1] (value.object): " + args[1]);
                console.log("  args[2] (key.countAndFlagsBits): 0x" + args[2].toString(16));
                console.log("  args[3] (key.object): " + args[3]);
                console.log("  args[4] (ignoringSync): " + args[4]);

            } catch (e) {
                console.log("[!] Hook处理出错: " + e.message);
                console.log("[!] 错误堆栈: " + e.stack);
            }
        },
        
        onLeave: function(retval) {
            console.log("[+] Keychain.set 执行完成");
            console.log("===============================\n");
        }
    });
    
    console.log("[+] Hook设置完成!");
    
} else {
    console.log("[!] 错误: 无法找到模块 " + moduleName);
    console.log("[!] 请检查应用名称是否正确");
    
    // 列出所有已加载的模块
    console.log("[*] 当前已加载的模块:");
    Process.enumerateModules().forEach(function(module) {
        console.log("  - " + module.name + " @ " + module.base);
    });
}

// 额外Hook: 也可以尝试通过符号名称Hook
try {
    var symbolAddress = Module.findExportByName(moduleName, "_$s14KeychainAccess0A0C3set_3key31ignoringAttributeSynchronizableySS_SSSbtKF");
    if (symbolAddress) {
        console.log("[+] 通过符号找到函数地址: " + symbolAddress);
        // 可以在这里添加额外的Hook逻辑
    }
} catch (e) {
    console.log("[*] 无法通过符号名称找到函数 (这是正常的)");
}

console.log("[*] 脚本加载完成，等待Keychain.set调用...");
