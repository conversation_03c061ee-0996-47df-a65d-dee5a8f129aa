// Frida脚本 - Hook UserKeyChain相关方法
console.log("[*] 开始Hook UserKeyChain方法...");

// 获取模块基址
var moduleName = "SkyCamera";
var baseAddress = Module.findBaseAddress(moduleName);

if (!baseAddress) {
    console.log("[!] 错误: 无法找到模块 " + moduleName);
    console.log("[!] 脚本退出");
} else {

console.log("[+] 找到模块基址: " + baseAddress);

// 定义要hook的方法列表
var methodsToHook = [
    {
        name: "+[UserKeyChain getBdTtsSn]",
        address: 0x3A6F34,
        description: "获取百度TTS SN"
    },
    {
        name: "-[UserKeyChain account]",
        address: 0x3A6AD4,
        description: "获取账户"
    },
    {
        name: "-[UserKeyChain setAccount:]",
        address: 0x3A6AEC,
        description: "设置账户"
    },
    {
        name: "-[UserKeyChain password]",
        address: 0x3A6B48,
        description: "获取密码"
    },
    {
        name: "-[User<PERSON>ey<PERSON>hain setPassword:]",
        address: 0x3A6B60,
        description: "设置密码"
    },
    {
        name: "-[UserKeyChain init]",
        address: 0x3A6DA4,
        description: "初始化"
    },
    {
        name: "+[UserKeyChain getKeyChainConfig]",
        address: 0x3A6DC8,
        description: "获取钥匙串配置"
    },
    {
        name: "+[UserKeyChain clearLocalKeyChainData]",
        address: 0x3A6DE0,
        description: "清除本地钥匙串数据"
    },
    {
        name: "+[UserKeyChain getBdTtsAppId]",
        address: 0x3A6E18,
        description: "获取百度TTS AppId"
    },
    {
        name: "+[UserKeyChain getBdTtsApiKey]",
        address: 0x3A6E70,
        description: "获取百度TTS ApiKey"
    },
    {
        name: "+[UserKeyChain getBdTtsSecretKey]",
        address: 0x3A6ED4,
        description: "获取百度TTS SecretKey"
    },
    {
        name: "+[UserKeyChain clearLocalKeyChainData]_0",
        address: 0x3ACF18,
        description: "清除本地钥匙串数据(实现)"
    }
];

// Hook每个方法
methodsToHook.forEach(function(method) {
    try {
        var targetAddress = baseAddress.add(method.address);
        console.log("[+] Hook " + method.name + " @ " + targetAddress);
        
        Interceptor.attach(targetAddress, {
            onEnter: function(args) {
                console.log("\n[*] ===== " + method.name + " 被调用 =====");
                console.log("[+] 描述: " + method.description);
                console.log("[+] 地址: " + targetAddress);
                
                // 打印参数
                if (method.name.indexOf("set") !== -1) {
                    // setter方法，打印设置的值
                    console.log("[+] 参数:");
                    console.log("  self: " + args[0]);
                    console.log("  _cmd: " + args[1]);
                    if (args[2]) {
                        try {
                            var value = ObjC.Object(args[2]);
                            console.log("  value: " + value.toString());
                        } catch (e) {
                            console.log("  value: " + args[2] + " (无法转换为ObjC对象)");
                        }
                    }
                } else {
                    // getter方法或其他方法
                    console.log("[+] 参数:");
                    console.log("  self: " + args[0]);
                    console.log("  _cmd: " + args[1]);
                }
                
                // 保存上下文用于onLeave
                this.methodName = method.name;
                this.description = method.description;
            },
            
            onLeave: function(retval) {
                console.log("[+] " + this.methodName + " 返回值:");
                
                if (retval && !retval.isNull()) {
                    try {
                        var objcRetval = ObjC.Object(retval);
                        var retString = objcRetval.toString();
                        console.log("  返回: " + retString);
                        
                        // 特别标记TTS相关的返回值
                        if (this.methodName.indexOf("getBdTts") !== -1) {
                            console.log("[!] *** TTS配置信息: " + retString + " ***");
                        }
                        
                    } catch (e) {
                        console.log("  返回: " + retval + " (原始指针)");
                    }
                } else {
                    console.log("  返回: null/void");
                }
                
                console.log("===============================\n");
            }
        });
        
    } catch (e) {
        console.log("[!] Hook " + method.name + " 失败: " + e.message);
    }
});

// 额外尝试通过ObjC运行时Hook
if (ObjC.available) {
    console.log("[*] 尝试通过ObjC运行时Hook...");
    
    try {
        var UserKeyChain = ObjC.classes.UserKeyChain;
        if (UserKeyChain) {
            console.log("[+] 找到UserKeyChain类");
            
            // Hook getBdTtsSecretKey方法
            var originalGetBdTtsSecretKey = UserKeyChain['+ getBdTtsSecretKey'];
            if (originalGetBdTtsSecretKey) {
                UserKeyChain['+ getBdTtsSecretKey'] = function() {
                    console.log("\n[*] ===== ObjC Runtime Hook: +[UserKeyChain getBdTtsSecretKey] =====");
                    var result = originalGetBdTtsSecretKey.call(this);
                    console.log("[!] *** getBdTtsSecretKey 返回: " + result + " ***");
                    console.log("===============================\n");
                    return result;
                };
                console.log("[+] 成功Hook getBdTtsSecretKey (ObjC Runtime)");
            }
            
            // Hook getBdTtsAppId方法
            var originalGetBdTtsAppId = UserKeyChain['+ getBdTtsAppId'];
            if (originalGetBdTtsAppId) {
                UserKeyChain['+ getBdTtsAppId'] = function() {
                    console.log("\n[*] ===== ObjC Runtime Hook: +[UserKeyChain getBdTtsAppId] =====");
                    var result = originalGetBdTtsAppId.call(this);
                    console.log("[!] *** getBdTtsAppId 返回: " + result + " ***");
                    console.log("===============================\n");
                    return result;
                };
                console.log("[+] 成功Hook getBdTtsAppId (ObjC Runtime)");
            }
            
            // Hook getBdTtsApiKey方法
            var originalGetBdTtsApiKey = UserKeyChain['+ getBdTtsApiKey'];
            if (originalGetBdTtsApiKey) {
                UserKeyChain['+ getBdTtsApiKey'] = function() {
                    console.log("\n[*] ===== ObjC Runtime Hook: +[UserKeyChain getBdTtsApiKey] =====");
                    var result = originalGetBdTtsApiKey.call(this);
                    console.log("[!] *** getBdTtsApiKey 返回: " + result + " ***");
                    console.log("===============================\n");
                    return result;
                };
                console.log("[+] 成功Hook getBdTtsApiKey (ObjC Runtime)");
            }
            
        } else {
            console.log("[!] 未找到UserKeyChain类");
        }
    } catch (e) {
        console.log("[!] ObjC Runtime Hook失败: " + e.message);
    }
} else {
    console.log("[!] ObjC运行时不可用");
}

console.log("[*] UserKeyChain Hook设置完成!");
console.log("[*] 等待方法调用...");
}
