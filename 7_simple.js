// 简化版TTS修复脚本 - 专注解决核心问题
console.log("[+] 开始简化TTS修复脚本...");

if (ObjC.available) {
    try {
        var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
        if (BDText2VoiceManager) {
            console.log("[+] 找到BDText2VoiceManager类");
            
            // 1. 强制修复TTS配置
            function forceTTSFix() {
                try {
                    var shareInstance = BDText2VoiceManager.shareInstance();
                    if (shareInstance) {
                        console.log("\n=== 强制修复TTS ===");
                        
                        // 强制设置配置成功
                        shareInstance.setValue_forKey_(ObjC.classes.NSNumber.numberWithBool_(true), "_isConfigTtsSuccess");
                        console.log("[修复] 已设置_isConfigTtsSuccess = 1");
                        
                        // 强制设置非静音
                        shareInstance.setIsMute_(false);
                        console.log("[修复] 已设置isMute = false");
                        
                        // 强制设置音频会话
                        try {
                            var AVAudioSession = ObjC.classes.AVAudioSession;
                            var sharedSession = AVAudioSession.sharedInstance();
                            var playbackCategory = ObjC.classes.NSString.stringWithString_("AVAudioSessionCategoryPlayback");
                            sharedSession.setCategory_(playbackCategory);
                            sharedSession.setActive_(true);
                            console.log("[修复] 已设置音频会话为播放模式");
                        } catch (e) {
                            console.log("[-] 音频会话设置失败: " + e);
                        }
                        
                        console.log("==================\n");
                    }
                } catch (e) {
                    console.log("[-] 强制修复失败: " + e);
                }
            }
            
            // 2. Hook playVoiceStr方法
            var playVoiceMethod = BDText2VoiceManager['- playVoiceStr:continuePlay:'];
            if (playVoiceMethod) {
                Interceptor.attach(playVoiceMethod.implementation, {
                    onEnter: function(args) {
                        var text = ObjC.Object(args[2]);
                        console.log("[TTS] 播放语音: '" + text + "'");
                        
                        // 播放前再次确保配置正确
                        try {
                            var instance = new ObjC.Object(args[0]);
                            instance.setValue_forKey_(ObjC.classes.NSNumber.numberWithBool_(true), "_isConfigTtsSuccess");
                            instance.setIsMute_(false);
                        } catch (e) {
                            console.log("[-] 播放前修复失败: " + e);
                        }
                    },
                    onLeave: function(retval) {
                        console.log("[TTS] 播放完成，返回值: " + retval);
                    }
                });
            }
            
            // 3. Hook语音播报检查函数，强制返回成功
            try {
                var baseAddress = Module.findBaseAddress("SkyCamera");
                if (baseAddress) {
                    var checkFunction = baseAddress.add(0x3C9930);
                    Interceptor.attach(checkFunction, {
                        onLeave: function(retval) {
                            if (retval.toInt32() === 0) {
                                console.log("[强制] 检查函数返回0，强制改为1");
                                retval.replace(ptr(1));
                            }
                        }
                    });
                    console.log("[+] 已hook语音检查函数");
                }
            } catch (e) {
                console.log("[-] Hook检查函数失败: " + e);
            }
            
            // 4. 直接调用TTS播放的函数
            function directPlayTTS(text) {
                try {
                    console.log("\n=== 直接调用TTS ===");
                    var shareInstance = BDText2VoiceManager.shareInstance();
                    if (shareInstance) {
                        // 确保配置正确
                        shareInstance.setValue_forKey_(ObjC.classes.NSNumber.numberWithBool_(true), "_isConfigTtsSuccess");
                        shareInstance.setIsMute_(false);
                        
                        // 直接调用播放
                        var textStr = ObjC.classes.NSString.stringWithString_(text);
                        shareInstance.playVoiceStr_continuePlay_(textStr, true);
                        console.log("[直接] 已调用播放: " + text);
                    }
                    console.log("==================\n");
                } catch (e) {
                    console.log("[-] 直接调用失败: " + e);
                }
            }
            
            // 5. 尝试使用系统TTS作为对比
            function testSystemTTS() {
                try {
                    console.log("\n=== 测试系统TTS ===");
                    var AVSpeechSynthesizer = ObjC.classes.AVSpeechSynthesizer;
                    var AVSpeechUtterance = ObjC.classes.AVSpeechUtterance;
                    
                    if (AVSpeechSynthesizer && AVSpeechUtterance) {
                        var synthesizer = AVSpeechSynthesizer.alloc().init();
                        var utterance = AVSpeechUtterance.speechUtteranceWithString_("系统测试");
                        synthesizer.speakUtterance_(utterance);
                        console.log("[系统] 系统TTS播放命令已发送");
                    }
                    console.log("==================\n");
                } catch (e) {
                    console.log("[-] 系统TTS测试失败: " + e);
                }
            }
            
            // 执行修复和测试
            setTimeout(forceTTSFix, 1000);           // 1秒后强制修复
            setTimeout(testSystemTTS, 3000);         // 3秒后测试系统TTS
            setTimeout(function() {                  // 5秒后测试百度TTS
                directPlayTTS("百度测试");
            }, 5000);
            
        } else {
            console.log("[-] 未找到BDText2VoiceManager类");
        }
        
    } catch (e) {
        console.log("[-] 脚本执行出错: " + e);
    }
    
} else {
    console.log("[-] Objective-C运行时不可用");
}

console.log("[+] 简化TTS修复脚本加载完成");
