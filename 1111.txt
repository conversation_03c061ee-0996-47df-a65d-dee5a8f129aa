追踪TTS配置状态变化...
=== 监控所有TTS方法调用 ===
BDText2VoiceManager所有方法:
  - + shareInstance
  - - init
  - - setupTTSConfig
  - - configureOnlineTTS
  - - configureOfflineTTS
  - - synthesizerStartWorkingSentence:
  - - synthesizerFinishWorkingSentence:
  - - synthesizerSpeechStartSentence:
  - - synthesizerSpeechEndSentence:
  - - isBluetoothHeadsetConnected
  - - canPlay:
  - - stopAll
  - - playVoiceStr:continuePlay:
  - - playVoice
  - - pause
  - - resume
  - - changeVoiceLanguage:
  - - changeVoiceSource:
  - - changeVoiceSpeed:
  - - changeVolume:
  - - changeGaoYin:
  - - changePlayModel:
  - - startPlayIntervalTimer
  - - stopPlayIntervalTimer
  - - playIntervalCountDownAction
  - - setTTSParam:type:
  - - updateConfig:
  - - updateTTSConfig
  - - playModel
  - - setPlayModel:
  - - isMute
  - - setIsMute:
  - - playing
  - - setPlaying:
  - - voiceArray
  - - setVoiceArray:
  - - playIntervalTimer
  - - setPlayIntervalTimer:
  - - playInterval
  - - setPlayInterval:
  - - ttsSourceType
  - - setTtsSourceType:
  - - muteListener
  - - setMuteListener:
  - - isConfigTtsSuccess
  - - setIsConfigTtsSuccess:
  - - .cxx_destruct
检查单例方法...
找到shareInstance方法
=== 监控语音合成器委托回调 ===
找到合成器相关类: BDSSpeechSynthesizer
找到合成器相关类: SpeechSynthesizerManager
找到合成器相关类: AVSpeechSynthesizer
找到合成器相关类: VSSpeechSynthesizer
找到合成器相关类: VSSpeechSynthesizerPreference
监控已启动，请点击测试按钮
=== 尝试修复播放模式 ===
