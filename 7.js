// Frida Hook Script for TTS Status Monitoring
// 监控 BDText2VoiceManager 和 ScanResultController 的 TTS 相关状态

console.log("[+] 开始TTS状态监控脚本...");

// Hook BDText2VoiceManager 类
if (ObjC.available) {
    try {
        // 获取 BDText2VoiceManager 类
        var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
        if (BDText2VoiceManager) {
            console.log("[+] 找到BDText2VoiceManager类");

            // Hook shareInstance 方法来获取实例
            var shareInstance = BDText2VoiceManager['+ shareInstance'];
            if (shareInstance) {
                Interceptor.attach(shareInstance.implementation, {
                    onEnter: function(args) {
                        console.log("[+] BDText2VoiceManager shareInstance被调用");
                    },
                    onLeave: function(retval) {
                        if (retval) {
                            var instance = new ObjC.Object(retval);
                            console.log("[+] BDText2VoiceManager实例: " + instance);

                            // 检查 _isConfigTtsSuccess 属性
                            try {
                                var isConfigTtsSuccess = instance.valueForKey_("_isConfigTtsSuccess");
                                console.log("[TTS] TTS配置是否成功(_isConfigTtsSuccess): " + isConfigTtsSuccess);

                                // 强制设置为成功状态
                                if (isConfigTtsSuccess == 0) {
                                    console.log("[修复] 强制设置_isConfigTtsSuccess为1");
                                    instance.setValue_forKey_(ObjC.classes.NSNumber.numberWithBool_(true), "_isConfigTtsSuccess");

                                    // 验证修改是否成功
                                    var newValue = instance.valueForKey_("_isConfigTtsSuccess");
                                    console.log("[修复] 修改后的_isConfigTtsSuccess: " + newValue);
                                }
                            } catch (e) {
                                console.log("[-] 读取_isConfigTtsSuccess出错: " + e);
                            }

                            // 检查 isMute 状态
                            try {
                                var isMute = instance.isMute();
                                console.log("[TTS] 是否静音(isMute): " + isMute);
                            } catch (e) {
                                console.log("[-] 读取isMute状态出错: " + e);
                            }
                        }
                    }
                });
            }
            
            // Hook isMute 方法
            var isMuteMethod = BDText2VoiceManager['- isMute'];
            if (isMuteMethod) {
                Interceptor.attach(isMuteMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[+] BDText2VoiceManager isMute方法被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[TTS] isMute返回结果: " + retval);

                        // 强制返回false(0)，确保不静音
                        if (retval.toInt32() !== 0) {
                            console.log("[修复] 检测到静音状态，强制设置为非静音");
                            retval.replace(ptr(0));
                            console.log("[修复] 强制修改isMute返回值为: " + retval);
                        }
                    }
                });
            }

            // Hook setIsMute 方法
            var setIsMuteMethod = BDText2VoiceManager['- setIsMute:'];
            if (setIsMuteMethod) {
                Interceptor.attach(setIsMuteMethod.implementation, {
                    onEnter: function(args) {
                        var muteValue = args[2];
                        console.log("[TTS] 设置静音状态(setIsMute): " + muteValue);

                        // 强制阻止设置为静音状态
                        if (muteValue.toInt32() !== 0) {
                            console.log("[拦截] 阻止设置为静音状态，强制改为非静音");
                            args[2] = ptr(0); // 强制设置为false
                        }
                    }
                });
            }

            // Hook playVoiceStr:continuePlay: 方法
            var playVoiceMethod = BDText2VoiceManager['- playVoiceStr:continuePlay:'];
            if (playVoiceMethod) {
                Interceptor.attach(playVoiceMethod.implementation, {
                    onEnter: function(args) {
                        var text = ObjC.Object(args[2]);
                        var continuePlay = args[3];
                        console.log("[TTS] 播放语音(playVoiceStr)，文本: '" + text + "', 继续播放: " + continuePlay);

                        // 保存实例引用，用于后续检查
                        this.instance = new ObjC.Object(args[0]);
                    },
                    onLeave: function(retval) {
                        console.log("[TTS] 语音播放完成，返回值: " + retval);

                        // 简单的播放后状态检查
                        try {
                            if (this.instance) {
                                // 只检查基本的播放状态
                                try {
                                    var isPlaying = this.instance.isPlaying();
                                    console.log("[TTS] 播放后状态 - isPlaying: " + isPlaying);
                                } catch (e) {
                                    console.log("[TTS] 无法检查播放状态");
                                }
                            }
                        } catch (e) {
                            console.log("[-] 播放后检查失败: " + e);
                        }
                    }
                });
            }

            // Hook updateTTSConfig 方法
            var updateTTSConfigMethod = BDText2VoiceManager['- updateTTSConfig'];
            if (updateTTSConfigMethod) {
                Interceptor.attach(updateTTSConfigMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[TTS] 更新TTS配置(updateTTSConfig)被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[TTS] TTS配置更新完成");

                        // 重新检查配置状态
                        var instance = new ObjC.Object(args[0]);
                        try {
                            var isConfigTtsSuccess = instance.valueForKey_("_isConfigTtsSuccess");
                            console.log("[TTS] 配置更新后的状态(_isConfigTtsSuccess): " + isConfigTtsSuccess);
                        } catch (e) {
                            console.log("[-] 配置更新后读取_isConfigTtsSuccess出错: " + e);
                        }
                    }
                });
            }

            // Hook 可能的TTS初始化方法
            var initTTSMethod = BDText2VoiceManager['- initTTS'];
            if (initTTSMethod) {
                Interceptor.attach(initTTSMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[TTS] TTS初始化(initTTS)被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[TTS] TTS初始化完成，返回值: " + retval);
                    }
                });
            }

            // Hook 可能的错误回调方法
            var errorCallbackMethod = BDText2VoiceManager['- onError:'];
            if (errorCallbackMethod) {
                Interceptor.attach(errorCallbackMethod.implementation, {
                    onEnter: function(args) {
                        try {
                            var error = new ObjC.Object(args[2]);
                            console.log("[错误] TTS错误回调: " + error);
                        } catch (e) {
                            console.log("[错误] TTS错误回调被调用，但无法解析错误信息");
                        }
                    }
                });
            }

        } else {
            console.log("[-] 未找到BDText2VoiceManager类");
        }

        // Hook setValue:forKey: 方法来拦截对_isConfigTtsSuccess的修改
        try {
            var NSObject = ObjC.classes.NSObject;
            if (NSObject) {
                var setValueForKeyMethod = NSObject['- setValue:forKey:'];
                if (setValueForKeyMethod) {
                    Interceptor.attach(setValueForKeyMethod.implementation, {
                        onEnter: function(args) {
                            try {
                                var key = new ObjC.Object(args[3]);
                                var keyStr = key.toString();

                                if (keyStr === "_isConfigTtsSuccess") {
                                    var value = new ObjC.Object(args[2]);
                                    console.log("[拦截] 检测到设置_isConfigTtsSuccess = " + value);

                                    // 如果试图设置为false(0)，强制改为true(1)
                                    if (value.boolValue() === false) {
                                        console.log("[拦截] 阻止设置_isConfigTtsSuccess为false，强制改为true");
                                        args[2] = ObjC.classes.NSNumber.numberWithBool_(true);
                                    }
                                }
                            } catch (e) {
                                // 忽略解析错误
                            }
                        }
                    });
                }
            }
        } catch (e) {
            console.log("[-] Hook setValue:forKey:出错: " + e);
        }

    } catch (e) {
        console.log("[-] Hook BDText2VoiceManager出错: " + e);
    }

    // Hook ScanResultController 类
    try {
        var ScanResultController = ObjC.classes.ScanResultController;
        if (ScanResultController) {
            console.log("[+] 找到ScanResultController类");

            // Hook viewWillAppear 方法
            var viewWillAppearMethod = ScanResultController['- viewWillAppear:'];
            if (viewWillAppearMethod) {
                Interceptor.attach(viewWillAppearMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[+] ScanResultController viewWillAppear被调用");

                        // 检查 tts_play_scan_result 配置
                        try {
                            // 获取 NSUserDefaults
                            var userDefaults = ObjC.classes.NSUserDefaults.standardUserDefaults();
                            var ttsPlayScanResult = userDefaults.boolForKey_("tts_play_scan_result");
                            console.log("[配置] 扫描结果语音播报(tts_play_scan_result): " + ttsPlayScanResult);

                            // 也尝试获取其他可能的配置键
                            var ttsEnabled = userDefaults.boolForKey_("tts_enabled");
                            console.log("[配置] TTS启用状态(tts_enabled): " + ttsEnabled);

                            var voiceEnabled = userDefaults.boolForKey_("voice_enabled");
                            console.log("[配置] 语音启用状态(voice_enabled): " + voiceEnabled);

                        } catch (e) {
                            console.log("[-] 读取tts_play_scan_result配置出错: " + e);
                        }
                    },
                    onLeave: function(retval) {
                        console.log("[+] ScanResultController viewWillAppear完成");
                    }
                });
            }

            // Hook viewDidLoad 方法
            var viewDidLoadMethod = ScanResultController['- viewDidLoad'];
            if (viewDidLoadMethod) {
                Interceptor.attach(viewDidLoadMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[+] ScanResultController viewDidLoad被调用");
                    }
                });
            }

        } else {
            console.log("[-] 未找到ScanResultController类");
        }

    } catch (e) {
        console.log("[-] Hook ScanResultController出错: " + e);
    }

    // Hook 语音播报检查函数 (基于IDA分析的函数地址)
    try {
        // Hook sub_1003C9930 函数 (语音播报检查函数)
        var checkVoicePlayFunction = Module.findExportByName(null, "sub_1003C9930");
        if (!checkVoicePlayFunction) {
            // 尝试通过模块基址 + 偏移来定位
            var baseAddress = Module.findBaseAddress("SkyCamera");
            if (baseAddress) {
                checkVoicePlayFunction = baseAddress.add(0x3C9930);
            }
        }

        if (checkVoicePlayFunction) {
            console.log("[+] 找到语音播报检查函数，地址: " + checkVoicePlayFunction);
            Interceptor.attach(checkVoicePlayFunction, {
                onEnter: function(args) {
                    console.log("[检查] 语音播报检查函数被调用");
                },
                onLeave: function(retval) {
                    console.log("[检查] 原始检查结果: " + retval + " (0=不播放, 1=可播放)");
                    // 强制返回1，让检查总是通过
                    retval.replace(ptr(1));
                    console.log("[强制] 已强制检查结果为: 1 (强制通过)");
                }
            });
        } else {
            console.log("[-] 未找到语音播报检查函数");
        }

    } catch (e) {
        console.log("[-] Hook语音播报检查函数出错: " + e);
    }

    // Hook 语音播报包装函数 (基于IDA分析)
    try {
        // Hook sub_100283E14 函数 (语音播报包装函数)
        var baseAddress = Module.findBaseAddress("SkyCamera");
        if (baseAddress) {
            var voiceWrapperFunction = baseAddress.add(0x283E14);
            console.log("[+] Hook语音播报包装函数，地址: " + voiceWrapperFunction);

            Interceptor.attach(voiceWrapperFunction, {
                onEnter: function(args) {
                    console.log("[包装] 语音播报包装函数被调用");
                    // args[0] = self, args[1] = selector, args[2] = text string
                    try {
                        if (args[2]) {
                            var textObj = new ObjC.Object(args[2]);
                            console.log("[包装] 要播报的文本: '" + textObj + "'");
                        }
                    } catch (e) {
                        console.log("[-] 读取文本参数出错: " + e);
                    }
                },
                onLeave: function(retval) {
                    console.log("[包装] 语音播报包装函数完成");
                }
            });
        }

    } catch (e) {
        console.log("[-] Hook语音播报包装函数出错: " + e);
    }

    // Hook NSUserDefaults 来监控配置变化
    try {
        var NSUserDefaults = ObjC.classes.NSUserDefaults;
        if (NSUserDefaults) {
            // Hook setBool:forKey: 方法
            var setBoolForKeyMethod = NSUserDefaults['- setBool:forKey:'];
            if (setBoolForKeyMethod) {
                Interceptor.attach(setBoolForKeyMethod.implementation, {
                    onEnter: function(args) {
                        var boolValue = args[2];
                        var key = new ObjC.Object(args[3]);
                        var keyStr = key.toString();

                        // 只监控TTS相关的配置
                        if (keyStr.includes("tts") || keyStr.includes("voice") || keyStr.includes("mute")) {
                            console.log("[配置] 设置配置项 " + keyStr + " = " + boolValue);
                        }
                    }
                });
            }

            // Hook boolForKey: 方法
            var boolForKeyMethod = NSUserDefaults['- boolForKey:'];
            if (boolForKeyMethod) {
                Interceptor.attach(boolForKeyMethod.implementation, {
                    onEnter: function(args) {
                        var key = new ObjC.Object(args[2]);
                        var keyStr = key.toString();

                        // 只监控TTS相关的配置读取
                        if (keyStr.includes("tts") || keyStr.includes("voice") || keyStr.includes("mute")) {
                            this.ttsKey = keyStr;
                        }
                    },
                    onLeave: function(retval) {
                        if (this.ttsKey) {
                            console.log("[配置] 读取配置项 " + this.ttsKey + " = " + retval);
                        }
                    }
                });
            }
        }

    } catch (e) {
        console.log("[-] Hook NSUserDefaults出错: " + e);
    }

    // 强制修复TTS配置的函数
    function forceTTSConfigSuccess() {
        try {
            var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
            if (BDText2VoiceManager) {
                var shareInstance = BDText2VoiceManager.shareInstance();
                if (shareInstance) {
                    console.log("\n=== 强制修复TTS配置 ===");

                    // 强制设置_isConfigTtsSuccess为true
                    try {
                        shareInstance.setValue_forKey_(ObjC.classes.NSNumber.numberWithBool_(true), "_isConfigTtsSuccess");
                        console.log("[修复] 已强制设置_isConfigTtsSuccess = 1");

                        // 验证修改
                        var newValue = shareInstance.valueForKey_("_isConfigTtsSuccess");
                        console.log("[修复] 验证修复结果: " + newValue);

                    } catch (e) {
                        console.log("[-] 强制修复失败: " + e);
                    }

                    // 强制设置isMute为false
                    try {
                        shareInstance.setIsMute_(false);
                        console.log("[修复] 已强制设置isMute = false");

                        // 验证isMute状态
                        var muteStatus = shareInstance.isMute();
                        console.log("[修复] 验证静音状态: " + muteStatus);

                    } catch (e) {
                        console.log("[-] 设置isMute失败: " + e);
                    }

                    // 强制设置相关配置项
                    try {
                        var userDefaults = ObjC.classes.NSUserDefaults.standardUserDefaults();

                        // 强制启用所有TTS相关配置
                        userDefaults.setBool_forKey_(true, "tts_play_scan_result");
                        userDefaults.setBool_forKey_(true, "tts_enabled");
                        userDefaults.setBool_forKey_(true, "voice_enabled");
                        userDefaults.synchronize();

                        console.log("[修复] 已强制启用所有TTS相关配置");
                        console.log("[修复] TTS配置强制修复完成");
                    } catch (e) {
                        console.log("[-] 其他配置修复失败: " + e);
                    }

                    console.log("=====================\n");
                }
            }
        } catch (e) {
            console.log("[-] 强制修复TTS配置出错: " + e);
        }
    }

    // 定期检查TTS状态的函数
    function checkTTSStatus() {
        try {
            var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
            if (BDText2VoiceManager) {
                var shareInstance = BDText2VoiceManager.shareInstance();
                if (shareInstance) {
                    console.log("\n=== TTS状态检查 ===");

                    // 检查 _isConfigTtsSuccess
                    try {
                        var isConfigTtsSuccess = shareInstance.valueForKey_("_isConfigTtsSuccess");
                        console.log("[状态] TTS配置是否成功(_isConfigTtsSuccess): " + isConfigTtsSuccess);

                        // 如果配置失败，强制设置为成功
                        if (isConfigTtsSuccess == 0) {
                            console.log("[修复] 检测到TTS配置失败，强制设置为成功状态");
                            shareInstance.setValue_forKey_(ObjC.classes.NSNumber.numberWithBool_(true), "_isConfigTtsSuccess");

                            // 验证修改
                            var newValue = shareInstance.valueForKey_("_isConfigTtsSuccess");
                            console.log("[修复] 强制修复后的_isConfigTtsSuccess: " + newValue);
                        }
                    } catch (e) {
                        console.log("[-] 无法读取_isConfigTtsSuccess: " + e);
                    }

                    // 检查 isMute
                    try {
                        var isMute = shareInstance.isMute();
                        console.log("[状态] 是否静音(isMute): " + isMute);

                        // 如果处于静音状态，强制设置为非静音
                        if (isMute) {
                            console.log("[修复] 检测到静音状态，强制设置为非静音");
                            shareInstance.setIsMute_(false);

                            // 验证修改
                            var newMuteStatus = shareInstance.isMute();
                            console.log("[修复] 强制修复后的静音状态: " + newMuteStatus);
                        }
                    } catch (e) {
                        console.log("[-] 无法读取isMute状态: " + e);
                    }

                    // 检查配置
                    var userDefaults = ObjC.classes.NSUserDefaults.standardUserDefaults();
                    var ttsPlayScanResult = userDefaults.boolForKey_("tts_play_scan_result");
                    console.log("[状态] 扫描结果语音播报(tts_play_scan_result): " + ttsPlayScanResult);

                    // 如果配置为false，强制设置为true
                    if (!ttsPlayScanResult) {
                        console.log("[修复] 检测到tts_play_scan_result为false，强制设置为true");
                        userDefaults.setBool_forKey_(true, "tts_play_scan_result");
                        userDefaults.synchronize();

                        // 验证修改
                        var newValue = userDefaults.boolForKey_("tts_play_scan_result");
                        console.log("[修复] 强制修复后的tts_play_scan_result: " + newValue);
                    }

                    console.log("==================\n");
                }
            }
        } catch (e) {
            console.log("[-] TTS状态检查出错: " + e);
        }
    }

    // 立即执行强制修复
    setTimeout(forceTTSConfigSuccess, 1000);

    // 每10秒检查一次TTS状态
    setInterval(checkTTSStatus, 10000);

    // 立即执行一次检查
    setTimeout(checkTTSStatus, 2000);

    // 每30秒执行一次强制修复（确保配置始终为成功状态）
    setInterval(forceTTSConfigSuccess, 30000);

} else {
    console.log("[-] Objective-C运行时不可用");
}

console.log("[+] TTS状态监控脚本加载成功!");

// 额外的hook来监控可能的错误和授权问题
if (ObjC.available) {

    // Hook 音频相关的类和方法
    try {
        // Hook AVAudioSession
        var AVAudioSession = ObjC.classes.AVAudioSession;
        if (AVAudioSession) {
            console.log("[+] 找到AVAudioSession类");

            var setActiveMethod = AVAudioSession['- setActive:error:'];
            if (setActiveMethod) {
                Interceptor.attach(setActiveMethod.implementation, {
                    onEnter: function(args) {
                        var active = args[2];
                        console.log("[音频] AVAudioSession setActive: " + active);
                    },
                    onLeave: function(retval) {
                        console.log("[音频] AVAudioSession setActive 结果: " + retval);
                    }
                });
            }

            var setCategoryMethod = AVAudioSession['- setCategory:error:'];
            if (setCategoryMethod) {
                Interceptor.attach(setCategoryMethod.implementation, {
                    onEnter: function(args) {
                        var category = new ObjC.Object(args[2]);
                        var categoryStr = category.toString();
                        console.log("[音频] AVAudioSession setCategory: " + categoryStr);

                        // 如果设置为Ambient模式，强制改为Playback模式
                        if (categoryStr === "AVAudioSessionCategoryAmbient") {
                            console.log("[拦截] 检测到Ambient模式，强制改为Playback模式");
                            var playbackCategory = ObjC.classes.NSString.stringWithString_("AVAudioSessionCategoryPlayback");
                            args[2] = playbackCategory;
                            console.log("[拦截] 已强制修改音频类别为: AVAudioSessionCategoryPlayback");
                        }
                        // 如果设置为其他非播放模式，也强制改为Playback模式
                        else if (categoryStr !== "AVAudioSessionCategoryPlayback" && categoryStr !== "AVAudioSessionCategoryPlayAndRecord") {
                            console.log("[拦截] 检测到非播放模式: " + categoryStr + "，强制改为Playback模式");
                            var playbackCategory = ObjC.classes.NSString.stringWithString_("AVAudioSessionCategoryPlayback");
                            args[2] = playbackCategory;
                            console.log("[拦截] 已强制修改音频类别为: AVAudioSessionCategoryPlayback");
                        }
                    }
                });
            }
        }

        // Hook AVAudioPlayer
        var AVAudioPlayer = ObjC.classes.AVAudioPlayer;
        if (AVAudioPlayer) {
            console.log("[+] 找到AVAudioPlayer类");

            var playMethod = AVAudioPlayer['- play'];
            if (playMethod) {
                Interceptor.attach(playMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[音频] AVAudioPlayer play被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[音频] AVAudioPlayer play结果: " + retval);
                    }
                });
            }

            var prepareToPlayMethod = AVAudioPlayer['- prepareToPlay'];
            if (prepareToPlayMethod) {
                Interceptor.attach(prepareToPlayMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[音频] AVAudioPlayer prepareToPlay被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[音频] AVAudioPlayer prepareToPlay结果: " + retval);
                    }
                });
            }
        }

        // Hook AVSpeechSynthesizer (系统TTS)
        var AVSpeechSynthesizer = ObjC.classes.AVSpeechSynthesizer;
        if (AVSpeechSynthesizer) {
            console.log("[+] 找到AVSpeechSynthesizer类");

            var speakUtteranceMethod = AVSpeechSynthesizer['- speakUtterance:'];
            if (speakUtteranceMethod) {
                Interceptor.attach(speakUtteranceMethod.implementation, {
                    onEnter: function(args) {
                        var utterance = new ObjC.Object(args[2]);
                        console.log("[系统TTS] AVSpeechSynthesizer speakUtterance被调用");
                        try {
                            var speechString = utterance.speechString();
                            console.log("[系统TTS] 要播报的文本: " + speechString);
                        } catch (e) {
                            console.log("[系统TTS] 无法获取文本内容");
                        }
                    }
                });
            }
        }

    } catch (e) {
        console.log("[-] Hook音频相关类出错: " + e);
    }

    // Hook 百度TTS相关的更多方法
    try {
        var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
        if (BDText2VoiceManager) {

            // Hook 可能的播放状态相关方法
            var isPlayingMethod = BDText2VoiceManager['- isPlaying'];
            if (isPlayingMethod) {
                Interceptor.attach(isPlayingMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[百度TTS] isPlaying被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[百度TTS] isPlaying结果: " + retval);
                    }
                });
            }

            // Hook 可能的停止方法
            var stopMethod = BDText2VoiceManager['- stop'];
            if (stopMethod) {
                Interceptor.attach(stopMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[百度TTS] stop被调用");
                    }
                });
            }

            // Hook 可能的暂停方法
            var pauseMethod = BDText2VoiceManager['- pause'];
            if (pauseMethod) {
                Interceptor.attach(pauseMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[百度TTS] pause被调用");
                    }
                });
            }

            // Hook 可能的恢复方法
            var resumeMethod = BDText2VoiceManager['- resume'];
            if (resumeMethod) {
                Interceptor.attach(resumeMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[百度TTS] resume被调用");
                    }
                });
            }

            // Hook 可能的音量设置方法
            var setVolumeMethod = BDText2VoiceManager['- setVolume:'];
            if (setVolumeMethod) {
                Interceptor.attach(setVolumeMethod.implementation, {
                    onEnter: function(args) {
                        var volume = args[2];
                        console.log("[百度TTS] setVolume被调用，音量: " + volume);
                    }
                });
            }

            // Hook 可能的错误回调
            var onErrorMethod = BDText2VoiceManager['- onError:'];
            if (onErrorMethod) {
                Interceptor.attach(onErrorMethod.implementation, {
                    onEnter: function(args) {
                        try {
                            var error = new ObjC.Object(args[2]);
                            console.log("[百度TTS] 错误回调: " + error);

                            // 尝试获取错误详情
                            try {
                                var errorCode = error.code();
                                var errorDesc = error.localizedDescription();
                                console.log("[百度TTS] 错误代码: " + errorCode + ", 描述: " + errorDesc);
                            } catch (e) {
                                console.log("[百度TTS] 无法获取错误详情");
                            }
                        } catch (e) {
                            console.log("[百度TTS] 错误回调被调用，但无法解析");
                        }
                    }
                });
            }

            // Hook 可能的完成回调
            var onFinishMethod = BDText2VoiceManager['- onFinish'];
            if (onFinishMethod) {
                Interceptor.attach(onFinishMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[百度TTS] 播放完成回调");
                    }
                });
            }

            // Hook 可能的开始播放回调
            var onStartMethod = BDText2VoiceManager['- onStart'];
            if (onStartMethod) {
                Interceptor.attach(onStartMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[百度TTS] 开始播放回调");
                    }
                });
            }

            // Hook 可能的授权检查方法
            var checkLicenseMethod = BDText2VoiceManager['- checkLicense'];
            if (checkLicenseMethod) {
                Interceptor.attach(checkLicenseMethod.implementation, {
                    onEnter: function(args) {
                        console.log("[百度TTS] 授权检查被调用");
                    },
                    onLeave: function(retval) {
                        console.log("[百度TTS] 授权检查结果: " + retval);
                        // 如果授权检查失败，强制返回成功
                        if (retval.toInt32() === 0) {
                            console.log("[强制] 授权检查失败，强制返回成功");
                            retval.replace(ptr(1));
                        }
                    }
                });
            }
        }

    } catch (e) {
        console.log("[-] Hook百度TTS额外方法出错: " + e);
    }

    // 强制绕过所有TTS相关检查
    try {
        console.log("[+] 开始强制绕过所有TTS检查...");

        // 如果有其他检查函数，也强制返回成功
        var baseAddress = Module.findBaseAddress("SkyCamera");
        if (baseAddress) {
            // 可能的其他检查函数地址（根据IDA分析添加）
            var checkFunctions = [
                0x3C9930,  // 主要的语音播报检查函数
                // 可以根据需要添加其他检查函数的偏移
            ];

            checkFunctions.forEach(function(offset) {
                try {
                    var funcAddr = baseAddress.add(offset);
                    Interceptor.attach(funcAddr, {
                        onLeave: function(retval) {
                            // 强制所有检查函数返回1（成功）
                            if (retval.toInt32() === 0) {
                                console.log("[强制] 检查函数返回0，强制改为1");
                                retval.replace(ptr(1));
                            }
                        }
                    });
                    console.log("[+] 已hook检查函数: " + funcAddr);
                } catch (e) {
                    // 忽略无效地址
                }
            });
        }

    } catch (e) {
        console.log("[-] 强制绕过检查出错: " + e);
    }

    // 添加一个函数来检查音频状态
    function checkAudioStatus() {
        try {
            console.log("\n=== 音频状态检查 ===");

            // 检查AVAudioSession状态
            var AVAudioSession = ObjC.classes.AVAudioSession;
            if (AVAudioSession) {
                var sharedInstance = AVAudioSession.sharedInstance();
                if (sharedInstance) {
                    try {
                        var category = sharedInstance.category();
                        console.log("[音频] 当前音频类别: " + category);

                        var outputVolume = sharedInstance.outputVolume();
                        console.log("[音频] 输出音量: " + outputVolume);

                        var isOtherAudioPlaying = sharedInstance.isOtherAudioPlaying();
                        console.log("[音频] 其他音频是否在播放: " + isOtherAudioPlaying);

                    } catch (e) {
                        console.log("[-] 读取音频状态出错: " + e);
                    }
                }
            }

            // 检查系统音量
            try {
                var MPVolumeView = ObjC.classes.MPVolumeView;
                if (MPVolumeView) {
                    console.log("[音频] 找到MPVolumeView类");
                }
            } catch (e) {
                console.log("[-] MPVolumeView检查出错: " + e);
            }

            console.log("==================\n");

        } catch (e) {
            console.log("[-] 音频状态检查出错: " + e);
        }
    }

    // 强制设置音频会话
    function forceAudioSessionSetup() {
        try {
            console.log("\n=== 强制设置音频会话 ===");

            var AVAudioSession = ObjC.classes.AVAudioSession;
            if (AVAudioSession) {
                var sharedInstance = AVAudioSession.sharedInstance();
                if (sharedInstance) {
                    try {
                        // 设置音频类别为播放（支持语音）
                        var playbackCategory = ObjC.classes.NSString.stringWithString_("AVAudioSessionCategoryPlayback");
                        var errorPtr = Memory.alloc(Process.pointerSize);
                        errorPtr.writePointer(ptr(0));

                        var result1 = sharedInstance.setCategory_error_(playbackCategory, errorPtr);
                        console.log("[音频] 设置音频类别为Playback模式，结果: " + result1);

                        // 激活音频会话
                        errorPtr.writePointer(ptr(0)); // 重置错误指针
                        var result2 = sharedInstance.setActive_error_(true, errorPtr);
                        console.log("[音频] 激活音频会话，结果: " + result2);

                        // 验证当前音频类别
                        var currentCategory = sharedInstance.category();
                        console.log("[音频] 当前音频类别: " + currentCategory);

                    } catch (e) {
                        console.log("[-] 设置音频会话出错: " + e);
                    }
                }
            }

            console.log("=====================\n");

        } catch (e) {
            console.log("[-] 强制设置音频会话出错: " + e);
        }
    }

    // 在强制修复后检查音频状态
    setTimeout(checkAudioStatus, 3000);

    // 强制设置音频会话
    setTimeout(forceAudioSessionSetup, 2000);

    // Hook 底层音频函数
    try {
        // Hook AudioServicesPlaySystemSound
        var AudioServicesPlaySystemSound = Module.findExportByName("AudioToolbox", "AudioServicesPlaySystemSound");
        if (AudioServicesPlaySystemSound) {
            console.log("[+] 找到AudioServicesPlaySystemSound函数");
            Interceptor.attach(AudioServicesPlaySystemSound, {
                onEnter: function(args) {
                    var soundID = args[0];
                    console.log("[底层音频] AudioServicesPlaySystemSound被调用，soundID: " + soundID);
                }
            });
        }

        // Hook AudioQueueStart
        var AudioQueueStart = Module.findExportByName("AudioToolbox", "AudioQueueStart");
        if (AudioQueueStart) {
            console.log("[+] 找到AudioQueueStart函数");
            Interceptor.attach(AudioQueueStart, {
                onEnter: function(args) {
                    console.log("[底层音频] AudioQueueStart被调用");
                },
                onLeave: function(retval) {
                    console.log("[底层音频] AudioQueueStart结果: " + retval);
                }
            });
        }

        // Hook AudioUnitRender
        var AudioUnitRender = Module.findExportByName("AudioToolbox", "AudioUnitRender");
        if (AudioUnitRender) {
            console.log("[+] 找到AudioUnitRender函数");
            Interceptor.attach(AudioUnitRender, {
                onEnter: function(args) {
                    console.log("[底层音频] AudioUnitRender被调用");
                }
            });
        }

    } catch (e) {
        console.log("[-] Hook底层音频函数出错: " + e);
    }

    // 测试音频输出功能
    function testAudioOutput() {
        try {
            console.log("\n=== 测试音频输出 ===");

            // 尝试播放系统声音
            var AudioServicesPlaySystemSound = Module.findExportByName("AudioToolbox", "AudioServicesPlaySystemSound");
            if (AudioServicesPlaySystemSound) {
                console.log("[测试] 播放系统提示音...");
                var playSound = new NativeFunction(AudioServicesPlaySystemSound, 'void', ['uint32']);
                playSound(1007); // 系统提示音
                console.log("[测试] 系统提示音播放命令已发送");
            }

            // 尝试使用系统TTS
            try {
                var AVSpeechSynthesizer = ObjC.classes.AVSpeechSynthesizer;
                var AVSpeechUtterance = ObjC.classes.AVSpeechUtterance;

                if (AVSpeechSynthesizer && AVSpeechUtterance) {
                    console.log("[测试] 尝试使用系统TTS播放测试音频...");

                    var synthesizer = AVSpeechSynthesizer.alloc().init();
                    var utterance = AVSpeechUtterance.speechUtteranceWithString_("测试");

                    synthesizer.speakUtterance_(utterance);
                    console.log("[测试] 系统TTS播放命令已发送");
                }
            } catch (e) {
                console.log("[-] 系统TTS测试失败: " + e);
            }

            console.log("==================\n");

        } catch (e) {
            console.log("[-] 音频输出测试出错: " + e);
        }
    }

    // 5秒后执行音频测试
    setTimeout(testAudioOutput, 5000);

    // 直接强制调用语音播报的函数
    function forcePlayTTS(text) {
        try {
            console.log("\n=== 强制调用TTS播报 ===");
            console.log("[强制] 尝试播报文本: " + text);

            var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
            if (BDText2VoiceManager) {
                var shareInstance = BDText2VoiceManager.shareInstance();
                if (shareInstance) {
                    // 直接调用playVoiceStr方法，绕过所有检查
                    var textStr = ObjC.classes.NSString.stringWithString_(text);
                    shareInstance.playVoiceStr_continuePlay_(textStr, true);
                    console.log("[强制] 已直接调用playVoiceStr方法");
                }
            }

            console.log("========================\n");

        } catch (e) {
            console.log("[-] 强制调用TTS播报出错: " + e);
        }
    }

    // 安全的TTS状态检查
    function safeCheckTTSStatus() {
        try {
            console.log("\n=== 安全TTS状态检查 ===");

            var BDText2VoiceManager = ObjC.classes.BDText2VoiceManager;
            if (BDText2VoiceManager) {
                var shareInstance = BDText2VoiceManager.shareInstance();
                if (shareInstance) {
                    // 只检查已知安全的属性
                    try {
                        var isConfigTtsSuccess = shareInstance.valueForKey_("_isConfigTtsSuccess");
                        console.log("[安全] _isConfigTtsSuccess: " + isConfigTtsSuccess);
                    } catch (e) {
                        console.log("[-] 无法读取_isConfigTtsSuccess");
                    }

                    try {
                        var isMute = shareInstance.isMute();
                        console.log("[安全] isMute: " + isMute);
                    } catch (e) {
                        console.log("[-] 无法读取isMute");
                    }

                    // 检查类的方法列表
                    try {
                        console.log("[安全] BDText2VoiceManager实例: " + shareInstance);
                        console.log("[安全] 实例类型: " + shareInstance.$className);
                    } catch (e) {
                        console.log("[-] 无法获取实例信息");
                    }
                }
            }

            console.log("========================\n");

        } catch (e) {
            console.log("[-] 安全TTS状态检查出错: " + e);
        }
    }

    // 8秒后执行安全检查
    setTimeout(safeCheckTTSStatus, 8000);

    // 10秒后测试强制播报
    setTimeout(function() {
        forcePlayTTS("强制测试");
    }, 10000);
    try {
        // Hook 可能的错误处理函数
        var NSLog = Module.findExportByName("Foundation", "NSLog");
        if (NSLog) {
            Interceptor.attach(NSLog, {
                onEnter: function(args) {
                    try {
                        var format = new ObjC.Object(args[0]);
                        var message = format.toString();

                        // 只记录TTS相关的日志
                        if (message.includes("TTS") || message.includes("语音") || message.includes("voice") ||
                            message.includes("百度") || message.includes("授权") || message.includes("license")) {
                            console.log("[日志] NSLog: " + message);
                        }
                    } catch (e) {
                        // 忽略解析错误
                    }
                }
            });
        }

        // Hook 可能的网络请求相关函数
        var NSURLSession = ObjC.classes.NSURLSession;
        if (NSURLSession) {
            var dataTaskMethod = NSURLSession['- dataTaskWithRequest:completionHandler:'];
            if (dataTaskMethod) {
                Interceptor.attach(dataTaskMethod.implementation, {
                    onEnter: function(args) {
                        try {
                            var request = new ObjC.Object(args[2]);
                            var url = request.URL().absoluteString();

                            // 检查是否是TTS相关的网络请求
                            if (url.includes("baidu") || url.includes("tts") || url.includes("voice") || url.includes("speech")) {
                                console.log("[网络] TTS相关请求到: " + url);

                                // 检查请求头
                                try {
                                    var headers = request.allHTTPHeaderFields();
                                    console.log("[网络] 请求头: " + headers);
                                } catch (e) {
                                    console.log("[网络] 无法获取请求头");
                                }
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    },
                    onLeave: function(retval) {
                        // 保存任务引用以便后续监控
                        if (retval) {
                            try {
                                var task = new ObjC.Object(retval);
                                // 可以在这里添加对任务完成的监控
                            } catch (e) {
                                // 忽略
                            }
                        }
                    }
                });
            }
        }

        // Hook NSURLConnection (旧版网络请求)
        var NSURLConnection = ObjC.classes.NSURLConnection;
        if (NSURLConnection) {
            var sendSyncRequestMethod = NSURLConnection['+ sendSynchronousRequest:returningResponse:error:'];
            if (sendSyncRequestMethod) {
                Interceptor.attach(sendSyncRequestMethod.implementation, {
                    onEnter: function(args) {
                        try {
                            var request = new ObjC.Object(args[2]);
                            var url = request.URL().absoluteString();

                            if (url.includes("baidu") || url.includes("tts") || url.includes("voice")) {
                                console.log("[网络] 同步TTS请求到: " + url);
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                });
            }
        }

    } catch (e) {
        console.log("[-] 设置额外hook出错: " + e);
    }
}
