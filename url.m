//
//  url.m
//  url-hook
//
//  Created by 小七 on 2024/9/10.
//

#import "url.h"
#import "CaptainHook.h"
#import <objc/runtime.h>
#include <UIKit/UIKit.h>
#include <sys/mount.h>

// 全局变量保存处理后的消息
static NSString *globalEncodedMessage = nil;

@implementation url

CHDeclareClass(NSURL);

CHClassMethod1(NSURL *, NSURL, URLWithString, NSString *, URLString) {
    NSString *nsString = [NSString stringWithUTF8String:sub_16981()];
    //验证码
    if([URLString containsString:@"getUserCaptchaCode"]){
        URLString = @"http://154.219.99.113:2000/getUserCaptchaCode.php";
    }
    //注册账号
    if([URLString containsString:@"registerAccount"]){
        URLString = [NSString stringWithFormat:@"http://154.219.99.113:2000/registerAccount.php?sn=%@",nsString];
        
    }
    //注册账号判断
    if([URLString containsString:@"checkAccountRegisterAvailability"]){
        
        URLString = @"http://154.219.99.113:2000/checkAccountRegisterAvailability.php";
    }
    //登陆
    if([URLString containsString:@"loginAccount"]){
        URLString = [NSString stringWithFormat:@"http://154.219.99.113:2000/loginAccount.php?sn=%@",nsString];
        
    }
    
    //激活
    if([URLString containsString:@"activeAccount"]){
        URLString = [NSString stringWithFormat:@"http://154.219.99.113:2000/activeAccount.php?sn=%@",nsString];

    }
    //玩法
    if([URLString containsString:@"playRules/index"]){
        URLString = @"http://154.219.99.113:2000/indexs.php";
    }
    //报法
    if([URLString containsString:@"indexNew"]){
        URLString = @"http://154.219.99.113:2000/indexNew.php";
    }
    //修改用户名
    if([URLString containsString:@"updateUserInfo"]){
        URLString = @"http://154.219.99.113:2000/updateUserInfo.php";
    }
    
    //修改密码
    if([URLString containsString:@"editPwd"]){
        URLString = @"http://154.219.99.113:2000/editPwd.php";
    }
    
    //修改二次密码
    if([URLString containsString:@"editPassWord2"]){
        URLString = @"http://154.219.99.113:2000/editPassWord2.php";
    }
    
    //修改头像
    if([URLString containsString:@"getOssPolicy"]){
        URLString = @"http://154.219.99.113:2000/getOssPolicy.php";
    }
    //连接设备
    if([URLString containsString:@"getRelationAssistantUserInfo"]){
        URLString = @"http://154.219.99.113:2000/getRelationAssistantUserInfo.php";
    }
    
    //用户协议
    if([URLString containsString:@"agreement.html"]){
        URLString = @"http://154.219.99.113:2000/agreement.html";
    }
    
    //隐私政策
    if([URLString containsString:@"prolicy.html"]){
        URLString = @"http://154.219.99.113:2000/prolicy.html";
    }
    
    return CHSuper1(NSURL, URLWithString, URLString);
}

CHDeclareClass(UIDevice);

CHOptimizedMethod0(self, NSUUID *, UIDevice, identifierForVendor) {
    
    NSUUID *customIDFV = [[NSUUID alloc] initWithUUIDString:@"12345678-1234-1234-1234-123456789ABC"];
    
    
    return customIDFV;
}

CHDeclareClass(NSBundle);

CHOptimizedMethod2(self, NSString *, NSBundle, pathForResource, NSString *, name, ofType, NSString *, ext){
    if ([name containsString:@"embedded"] || [ext containsString:@"mobileprovision"]) {
        return nil;
    }
    return CHSuper2(NSBundle, pathForResource, name, ofType, ext);
}



// Hook SecurityAESTool 类
CHDeclareClass(SecurityAESTool);

CHOptimizedMethod4(self, id, SecurityAESTool, AES128operation, int, operation, data, NSData *, data, key, NSData *, key, iv, NSData *, iv) {
    

    if (data) {
        NSString *dataUtf8String = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        if (dataUtf8String) {
            if([dataUtf8String containsString:@"@@@@"]){
                // 将 NSString 转换为 NSData
                data = [globalEncodedMessage dataUsingEncoding:NSUTF8StringEncoding];
            }
        }
    }
    

     
     id result = CHSuper4(SecurityAESTool, AES128operation, operation, data, data, key, key, iv, iv);
     
     return result;
}

// Hook SocketRocketManager 类
CHDeclareClass(SocketRocketManager);

CHOptimizedMethod1(self, void, SocketRocketManager, sendMessage, id, message) {

    // base64编码
    NSData *data = [message dataUsingEncoding:NSUTF8StringEncoding];
    NSString *base64String = [data base64EncodedStringWithOptions:0];
    globalEncodedMessage = nil;
    // 保存到全局变量
    globalEncodedMessage = base64String;


    
    CHSuper1(SocketRocketManager, sendMessage, @"@@@@@@@@");
    
}
CHDeclareClass(UILabel);
CHMethod1(void, UILabel, setText, NSString*, title)

{

    // hasPrefix 开头几个
    // isEqualToString 全部内容
    // containsString 包含某个

    if([title containsString:@"预言家"]) {
        title = [title stringByReplacingOccurrencesOfString:@"预言家"withString:@"元宝相机"];
    }else if([title containsString:@"星夜相机"]) {
        title = [title stringByReplacingOccurrencesOfString:@"星夜相机"withString:@"元宝相机"];
    }
    return CHSuper1(UILabel, setText,title);

    
}
CHConstructor {
    CHLoadLateClass(NSURL);
    CHHook1(NSURL, URLWithString);
    CHLoadLateClass(UIDevice);
    CHClassHook0(UIDevice, identifierForVendor);
    CHLoadLateClass(NSBundle);
    CHHook2(NSBundle, pathForResource, ofType);
    

    // 加载并hook SecurityAESTool
    CHLoadLateClass(SecurityAESTool);
    CHHook4(SecurityAESTool, AES128operation, data, key, iv);

    // 加载并hook SocketRocketManager
    CHLoadLateClass(SocketRocketManager);
    CHHook1(SocketRocketManager, sendMessage);
    
    CHLoadLateClass(UILabel);
    CHHook1(UILabel, setText);
    
}

//获取特征码
static const char  * sub_16981(void){
    struct statfs buf;
    statfs("/", &buf);
    char* prefix = "com.apple.os.update-";
    if(strstr(buf.f_mntfromname, prefix)) {
        NSString *ocString1 = [[NSString alloc] initWithUTF8String:buf.f_mntfromname+strlen(prefix)];
        NSString *stringOne = [ocString1 substringToIndex:36];
        NSMutableString *ocString2 = [NSMutableString stringWithString:stringOne];
        [ocString2 replaceCharactersInRange:NSMakeRange(8, 1) withString:@"-"];
        [ocString2 replaceCharactersInRange:NSMakeRange(13, 1) withString:@"-"];
        [ocString2 replaceCharactersInRange:NSMakeRange(13, 1) withString:@"-"];
        [ocString2 replaceCharactersInRange:NSMakeRange(22, 1) withString:@"-"];
        const char *cString = (const char *)[ocString2 UTF8String];
        if(!cString){
            return "0000000000-00000-00000-0000000000000";
        }else{
            return cString;
        }
    } else {
        const char * ccc = sub_68841();
        return ccc;
    }
}

static const char *sub_68841(void){
    NSFileManager *fileManager=[NSFileManager defaultManager];
    NSData *data=[fileManager contentsAtPath:@"/var/mobile/Library/Logs/AppleSupport/general.log"];
    NSMutableString *string = [[NSMutableString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString *regex = @"serial\":\"(.*?)\"";
    NSError *error = nil;
    NSRegularExpression *re = [NSRegularExpression regularExpressionWithPattern:regex options:NSRegularExpressionCaseInsensitive error:&error];
    NSArray *result = [re matchesInString:string options:0 range:NSMakeRange(0, string.length)];
    for (NSTextCheckingResult *match in result) {
        NSString *serial = [string substringWithRange:[match rangeAtIndex:1]];
        const char *cString = (const char *)[serial UTF8String];
        
        if(!cString){
            return "0000000000-00000-00000-0000000000000";
            
        }else{
            return cString;
            
        }
    }
    return "0000000000-00000-00000-0000000000000";
}
@end
