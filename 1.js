// Frida脚本 - Hook CCCrypt函数
// 只监控解密操作

console.log("[*] 开始Hook CCCrypt函数 - 只监控解密操作...");

// 获取CCCrypt函数地址
var CCCrypt = Module.findExportByName("libcommonCrypto.dylib", "CCCrypt");

if (CCCrypt) {
    console.log("[+] 找到CCCrypt函数地址: " + CCCrypt);

    // Hook CCCrypt函数
    Interceptor.attach(CCCrypt, {
        onEnter: function(args) {
            // CCCrypt参数解析
            // CCCryptorStatus CCCrypt(
            //     CCOperation op,         /* kCCEncrypt, kCCDecrypt */
            //     CCAlgorithm alg,        /* kCCAlgorithmAES128, etc. */
            //     CCOptions options,      /* kCCOptionPKCS7Padding, etc. */
            //     const void *key,        /* raw key material */
            //     size_t keyLength,       /* length of key material */
            //     const void *iv,         /* initialization vector, optional */
            //     const void *dataIn,     /* optional per op and alg */
            //     size_t dataInLength,    /* length of data returned */
            //     void *dataOut,          /* data RETURNED here */
            //     size_t dataOutAvailable,/* IN: size of dataOut */
            //     size_t *dataOutMoved    /* OUT: CCCryptorStatus */
            // );

            this.op = args[0].toInt32();

            // 只处理解密操作 (op == 1 表示解密，op == 0 表示加密)
            if (this.op != 1) {
                this.isDecrypt = false;
                return; // 跳过加密操作
            }

            this.isDecrypt = true;
            this.alg = args[1].toInt32();
            this.options = args[2].toInt32();
            this.key = args[3];
            this.keyLength = args[4].toInt32();
            this.iv = args[5];
            this.dataIn = args[6];
            this.dataInLength = args[7].toInt32();
            this.dataOut = args[8];
            this.dataOutAvailable = args[9].toInt32();
            this.dataOutMoved = args[10];

            console.log("\n=== 解密操作 ===");
            console.log("[*] 操作类型: 解密");

            // 算法类型
            var algorithm = "";
            switch(this.alg) {
                case 0: algorithm = "AES128"; break;
                case 1: algorithm = "DES"; break;
                case 2: algorithm = "3DES"; break;
                case 3: algorithm = "CAST"; break;
                case 4: algorithm = "RC4"; break;
                case 5: algorithm = "RC2"; break;
                case 6: algorithm = "Blowfish"; break;
                default: algorithm = "Unknown(" + this.alg + ")"; break;
            }
            console.log("[*] 算法: " + algorithm);

            // 选项
         //   console.log("[*] 选项: 0x" + this.options.toString(16));

            // 打印Key (十六进制和字符串形式)
            if (!this.key.isNull() && this.keyLength > 0) {
                var keyData = Memory.readByteArray(this.key, this.keyLength);
                var keyBytes = new Uint8Array(keyData);
               // console.log("[*] Key长度: " + this.keyLength);



                // 尝试读取Key的字符串形式 - 先检查是否可能是字符串
                var isPossibleString = true;

                // 检查是否包含可打印字符
                for (var i = 0; i < keyBytes.length; i++) {
                    if (keyBytes[i] === 0) {
                        // 如果遇到null字符，检查是否是字符串结尾
                        if (i < keyBytes.length - 1) {
                            // 如果不是最后一个字符就是null，可能不是字符串
                            var hasNonZeroAfter = false;
                            for (var j = i + 1; j < keyBytes.length; j++) {
                                if (keyBytes[j] !== 0) {
                                    hasNonZeroAfter = true;
                                    break;
                                }
                            }
                            if (hasNonZeroAfter) {
                                isPossibleString = false;
                                break;
                            }
                        }
                    } else if (keyBytes[i] < 32 || keyBytes[i] > 126) {
                        // 如果包含不可打印字符（除了null），可能不是字符串
                        isPossibleString = false;
                        break;
                    }
                }

                if (isPossibleString) {
                    try {
                        var keyStr = Memory.readUtf8String(this.key, this.keyLength);
                        console.log("[*] Key(字符串): " + keyStr);
                    } catch(e) {
                        // 如果确实无法转换，则不打印错误信息
                    }
                }
            }

            // 打印IV (十六进制和字符串形式)
            if (!this.iv.isNull()) {
                // IV长度通常是16字节(AES)或8字节(DES)
                var ivLength = (this.alg == 0) ? 16 : 8;
              //  console.log("[*] IV长度: " + ivLength);


                // 读取IV数据
                var ivData = Memory.readByteArray(this.iv, ivLength);
                var ivBytes = new Uint8Array(ivData);

                // 尝试读取IV的字符串形式 - 先检查是否可能是字符串
                var isPossibleString = true;

                // 检查是否包含可打印字符
                for (var i = 0; i < ivBytes.length; i++) {
                    if (ivBytes[i] === 0) {
                        // 如果遇到null字符，检查是否是字符串结尾
                        if (i < ivBytes.length - 1) {
                            // 如果不是最后一个字符就是null，可能不是字符串
                            var hasNonZeroAfter = false;
                            for (var j = i + 1; j < ivBytes.length; j++) {
                                if (ivBytes[j] !== 0) {
                                    hasNonZeroAfter = true;
                                    break;
                                }
                            }
                            if (hasNonZeroAfter) {
                                isPossibleString = false;
                                break;
                            }
                        }
                    } else if (ivBytes[i] < 32 || ivBytes[i] > 126) {
                        // 如果包含不可打印字符（除了null），可能不是字符串
                        isPossibleString = false;
                        break;
                    }
                }

                if (isPossibleString) {
                    try {
                        var ivStr = Memory.readUtf8String(this.iv, ivLength);
                        console.log("[*] IV(字符串): " + ivStr);
                    } catch(e) {
                        // 如果确实无法转换，则不打印错误信息
                    }
                }
            } else {
                console.log("[*] IV: NULL");
            }

            // 打印输入数据 (加密前/解密前)
            if (!this.dataIn.isNull() && this.dataInLength > 0) {
                var inputLabel = this.op == 0 ? "加密前数据" : "解密前数据";
               // console.log("[*] " + inputLabel + "长度: " + this.dataInLength);

                // 读取解密前的二进制数据并转换为base64
                try {
                    var inputData = Memory.readByteArray(this.dataIn, this.dataInLength);
                    var inputBytes = new Uint8Array(inputData);

                    // 转换为base64
                    var base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
                    var base64 = "";
                    var i = 0;

                    while (i < inputBytes.length) {
                        var a = inputBytes[i++];
                        var b = i < inputBytes.length ? inputBytes[i++] : 0;
                        var c = i < inputBytes.length ? inputBytes[i++] : 0;

                        var bitmap = (a << 16) | (b << 8) | c;

                        base64 += base64Chars.charAt((bitmap >> 18) & 63);
                        base64 += base64Chars.charAt((bitmap >> 12) & 63);
                        base64 += (i - 2) < inputBytes.length ? base64Chars.charAt((bitmap >> 6) & 63) : "=";
                        base64 += (i - 1) < inputBytes.length ? base64Chars.charAt(bitmap & 63) : "=";
                    }

                    console.log("[*] " + inputLabel + "(base64): " + base64);
                } catch(e) {
                    console.log("[*] " + inputLabel + ": 无法读取数据");
                }
            }
        },

        onLeave: function(retval) {
            // 只处理解密操作
            if (!this.isDecrypt) {
                return;
            }

          //  console.log("[*] CCCrypt返回值: " + retval);

            // 打印解密后数据
            if (!this.dataOut.isNull() && !this.dataOutMoved.isNull()) {
                var outputLength = Memory.readULong(this.dataOutMoved);
                if (outputLength > 0) {
                   // console.log("[*] 解密后数据长度: " + outputLength);

                    // 尝试读取解密后数据的字符串形式
                    try {
                        var outputStr = Memory.readUtf8String(this.dataOut, outputLength);
                        console.log("[*] 解密后数据(字符串): " + outputStr);
                    } catch(e) {
                        // 如果不是有效的UTF8，尝试读取为ASCII
                        try {
                            var outputBytes = Memory.readByteArray(this.dataOut, outputLength);
                            var outputAscii = "";
                            var bytes = new Uint8Array(outputBytes);
                            for (var i = 0; i < bytes.length; i++) {
                                if (bytes[i] >= 32 && bytes[i] <= 126) {
                                    outputAscii += String.fromCharCode(bytes[i]);
                                } else {
                                    outputAscii += ".";
                                }
                            }
                            console.log("[*] 解密后数据(ASCII): " + outputAscii);
                        } catch(e2) {
                            console.log("[*] 解密后数据(字符串): 无法转换为字符串");
                        }
                    }
                }
            }

            console.log("=== 解密操作结束 ===\n");
        }
    });

    console.log("[+] CCCrypt Hook设置完成!");
} else {
    console.log("[-] 未找到CCCrypt函数");
}

// 也可以Hook其他相关的加密函数
console.log("[*] 尝试Hook其他加密相关函数...");

// // Hook CCCryptorCreate
// var CCCryptorCreate = Module.findExportByName("libcommonCrypto.dylib", "CCCryptorCreate");
// if (CCCryptorCreate) {
//     console.log("[+] 找到CCCryptorCreate函数");
//     Interceptor.attach(CCCryptorCreate, {
//         onEnter: function(args) {
//             console.log("\n=== CCCryptorCreate调用 ===");
//             var op = args[0].toInt32();
//             var operation = op == 0 ? "加密器" : "解密器";
//             console.log("[*] 创建: " + operation);
//         }
//     });
// }

// // Hook CCCryptorUpdate
// var CCCryptorUpdate = Module.findExportByName("libcommonCrypto.dylib", "CCCryptorUpdate");
// if (CCCryptorUpdate) {
//     console.log("[+] 找到CCCryptorUpdate函数");
//     Interceptor.attach(CCCryptorUpdate, {
//         onEnter: function(args) {
//             console.log("\n=== CCCryptorUpdate调用 ===");
//             this.dataIn = args[1];
//             this.dataInLength = args[2].toInt32();
//             this.dataOut = args[3];
//             this.dataOutMoved = args[5];

//             if (!this.dataIn.isNull() && this.dataInLength > 0) {
//                 var inputData = Memory.readByteArray(this.dataIn, this.dataInLength);
//                 var inputHex = Array.from(new Uint8Array(inputData))
//                     .map(b => b.toString(16).padStart(2, '0'))
//                     .join('');
//             }
//         }
//     });
// }

console.log("[*] Hook设置完成，等待解密操作...");