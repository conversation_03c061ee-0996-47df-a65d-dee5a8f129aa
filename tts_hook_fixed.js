// Frida脚本 - Hook TTS相关方法并固定返回值
console.log("[*] 开始Hook TTS相关方法...");

// 获取模块基址
var moduleName = "SkyCamera";
var baseAddress = Module.findBaseAddress(moduleName);

if (!baseAddress) {
    console.log("[!] 错误: 无法找到模块 " + moduleName);
    console.log("[!] 脚本退出");
} else {
    console.log("[+] 找到模块基址: " + baseAddress);

    // 定义固定的返回值
    var fixedValues = {
        appId: "119586418",
        apiKey: "AGi6H2xrXqFEj6SnDqJ4JG9K", 
        secretKey: "zQIwuSfSzYZLsIswajtY16CudBc3gFqP",
        sn: "9a2913c5-c52bbfb7-2949-024f-63a07-00"
    };

    // 定义要hook的TTS方法
    var ttsMethodsToHook = [
        {
            name: "+[User<PERSON><PERSON><PERSON><PERSON><PERSON> getBdTtsAppId]",
            address: 0x3A6E18,
            fixedValue: fixedValues.appId,
            description: "百度TTS AppId"
        },
        {
            name: "+[UserKeyChain getBdTtsApiKey]", 
            address: 0x3A6E70,
            fixedValue: fixedValues.apiKey,
            description: "百度TTS ApiKey"
        },
        {
            name: "+[UserKeyChain getBdTtsSecretKey]",
            address: 0x3A6ED4,
            fixedValue: fixedValues.secretKey,
            description: "百度TTS SecretKey"
        },
        {
            name: "+[UserKeyChain getBdTtsSn]",
            address: 0x3A6F34,
            fixedValue: fixedValues.sn,
            description: "百度TTS SN"
        }
    ];

    // Hook每个TTS方法
    ttsMethodsToHook.forEach(function(method) {
        try {
            var targetAddress = baseAddress.add(method.address);
            console.log("[+] Hook " + method.name + " @ " + targetAddress);
            
            Interceptor.attach(targetAddress, {
                onEnter: function(args) {
                    console.log("\n[*] ===== " + method.name + " 被调用 =====");
                    console.log("[+] " + method.description);
                    this.methodName = method.name;
                    this.fixedValue = method.fixedValue;
                },
                
                onLeave: function(retval) {
                    // 创建固定的NSString返回值
                    var fixedNSString = ObjC.classes.NSString.stringWithString_(this.fixedValue);
                    
                    console.log("[+] 原始返回值: " + (retval.isNull() ? "null" : ObjC.Object(retval).toString()));
                    console.log("[!] *** 固定返回值: " + this.fixedValue + " ***");
                    
                    // 替换返回值
                    retval.replace(fixedNSString);
                    
                    console.log("===============================\n");
                }
            });
            
        } catch (e) {
            console.log("[!] Hook " + method.name + " 失败: " + e.message);
        }
    });

    // Hook BDSSpeechSynthesizer 相关方法
    if (ObjC.available) {
        console.log("[*] 设置BDSSpeechSynthesizer Hook...");

        try {
            // 先检查类是否存在
            var BDSSpeechSynthesizer = ObjC.classes.BDSSpeechSynthesizer;
            if (BDSSpeechSynthesizer) {
                console.log("[+] 找到BDSSpeechSynthesizer类");

                // 列出所有可用的方法
                console.log("[*] BDSSpeechSynthesizer 可用方法:");
                var methods = BDSSpeechSynthesizer.$ownMethods;
                for (var i = 0; i < methods.length; i++) {
                    if (methods[i].indexOf("loadOfflineEngine") !== -1 ||
                        methods[i].indexOf("setApiKey") !== -1) {
                        console.log("  " + methods[i]);
                    }
                }

                // 使用Interceptor.attach Hook loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:withSn:
                try {
                    var loadOfflineEngineMethod = BDSSpeechSynthesizer['- loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:withSn:'];
                    if (loadOfflineEngineMethod) {
                        console.log("[+] 找到方法: loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:withSn:");

                        Interceptor.attach(loadOfflineEngineMethod.implementation, {
                            onEnter: function(args) {
                                console.log("\n[*] ===== BDSSpeechSynthesizer loadOfflineEngine 被调用 =====");
                                console.log("[+] 原始参数:");
                                console.log("  self: " + args[0]);
                                console.log("  _cmd: " + args[1]);
                                console.log("  engine: " + (args[2] ? ObjC.Object(args[2]).toString() : "null"));
                                console.log("  speechDataPath: " + (args[3] ? ObjC.Object(args[3]).toString() : "null"));
                                console.log("  licenseFilePath: " + (args[4] ? ObjC.Object(args[4]).toString() : "null"));
                                console.log("  appCode: " + (args[5] ? ObjC.Object(args[5]).toString() : "null"));
                                console.log("  sn: " + (args[6] ? ObjC.Object(args[6]).toString() : "null"));

                                // 只修改appCode参数，保持sn不变
                                var fixedAppCode = ObjC.classes.NSString.stringWithString_(fixedValues.appId);

                                args[5] = fixedAppCode;
                                // args[6] = sn; // 保持原始sn不变

                                console.log("[!] *** 修改参数 ***");
                                console.log("  固定appCode: " + fixedValues.appId);
                                console.log("  保持原始sn: " + (args[6] ? ObjC.Object(args[6]).toString() : "null"));
                            },
                            onLeave: function(retval) {
                                console.log("[+] 方法执行完成, 返回值: " + retval);
                                console.log("===============================\n");
                            }
                        });
                        console.log("[+] Hook loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:withSn: 成功");
                    } else {
                        console.log("[!] 未找到 loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:withSn: 方法");
                    }
                } catch (e) {
                    console.log("[!] Hook loadOfflineEngine 失败: " + e.message);
                }

                // 使用Interceptor.attach Hook loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:
                try {
                    var loadOfflineEngineMethod2 = BDSSpeechSynthesizer['- loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:'];
                    if (loadOfflineEngineMethod2) {
                        console.log("[+] 找到方法: loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode:");

                        Interceptor.attach(loadOfflineEngineMethod2.implementation, {
                            onEnter: function(args) {
                                console.log("\n[*] ===== BDSSpeechSynthesizer loadOfflineEngine (无sn) 被调用 =====");
                                console.log("[+] 原始参数:");
                                console.log("  self: " + args[0]);
                                console.log("  _cmd: " + args[1]);
                                console.log("  engine: " + (args[2] ? ObjC.Object(args[2]).toString() : "null"));
                                console.log("  speechDataPath: " + (args[3] ? ObjC.Object(args[3]).toString() : "null"));
                                console.log("  licenseFilePath: " + (args[4] ? ObjC.Object(args[4]).toString() : "null"));
                                console.log("  appCode: " + (args[5] ? ObjC.Object(args[5]).toString() : "null"));

                                // 修改appCode参数
                                var fixedAppCode = ObjC.classes.NSString.stringWithString_(fixedValues.appId);
                                args[5] = fixedAppCode;

                                console.log("[!] *** 修改参数 ***");
                                console.log("  固定appCode: " + fixedValues.appId);
                            },
                            onLeave: function(retval) {
                                console.log("[+] 方法执行完成, 返回值: " + retval);
                                console.log("===============================\n");
                            }
                        });
                        console.log("[+] Hook loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode: 成功");
                    } else {
                        console.log("[!] 未找到 loadOfflineEngine:speechDataPath:licenseFilePath:withAppCode: 方法");
                    }
                } catch (e) {
                    console.log("[!] Hook loadOfflineEngine (无sn) 失败: " + e.message);
                }

                // 使用Interceptor.attach Hook setApiKey:withSecretKey:
                try {
                    var setApiKeyMethod = BDSSpeechSynthesizer['- setApiKey:withSecretKey:'];
                    if (setApiKeyMethod) {
                        console.log("[+] 找到方法: setApiKey:withSecretKey:");

                        Interceptor.attach(setApiKeyMethod.implementation, {
                            onEnter: function(args) {
                                console.log("\n[*] ===== BDSSpeechSynthesizer setApiKey 被调用 =====");
                                console.log("[+] 原始参数:");
                                console.log("  self: " + args[0]);
                                console.log("  _cmd: " + args[1]);
                                console.log("  apiKey: " + (args[2] ? ObjC.Object(args[2]).toString() : "null"));
                                console.log("  secretKey: " + (args[3] ? ObjC.Object(args[3]).toString() : "null"));

                                // 修改apiKey和secretKey参数
                                var fixedApiKey = ObjC.classes.NSString.stringWithString_(fixedValues.apiKey);
                                var fixedSecretKey = ObjC.classes.NSString.stringWithString_(fixedValues.secretKey);

                                args[2] = fixedApiKey;
                                args[3] = fixedSecretKey;

                                console.log("[!] *** 修改参数 ***");
                                console.log("  固定apiKey: " + fixedValues.apiKey);
                                console.log("  固定secretKey: " + fixedValues.secretKey);
                            },
                            onLeave: function(retval) {
                                console.log("[+] 方法执行完成");
                                console.log("===============================\n");
                            }
                        });
                        console.log("[+] Hook setApiKey:withSecretKey: 成功");
                    } else {
                        console.log("[!] 未找到 setApiKey:withSecretKey: 方法");
                    }
                } catch (e) {
                    console.log("[!] Hook setApiKey 失败: " + e.message);
                }

            } else {
                console.log("[!] 未找到BDSSpeechSynthesizer类");
                console.log("[*] 尝试查找相似的类名...");

                // 搜索包含"Speech"或"TTS"的类
                var allClasses = Object.getOwnPropertyNames(ObjC.classes);
                var speechClasses = allClasses.filter(function(name) {
                    return name.toLowerCase().indexOf('speech') !== -1 ||
                           name.toLowerCase().indexOf('tts') !== -1 ||
                           name.toLowerCase().indexOf('synthesizer') !== -1;
                });

                if (speechClasses.length > 0) {
                    console.log("[*] 找到相关类:");
                    for (var n = 0; n < speechClasses.length && n < 10; n++) {
                        console.log("  " + speechClasses[n]);
                    }
                } else {
                    console.log("[!] 未找到相关的Speech/TTS类");
                }
            }
        } catch (e) {
            console.log("[!] BDSSpeechSynthesizer Hook失败: " + e.message);
            console.log("[!] 错误堆栈: " + e.stack);
        }
    }

    // 额外通过ObjC运行时Hook UserKeyChain（双重保险）
    if (ObjC.available) {
        console.log("[*] 设置ObjC运行时Hook作为备用...");

        try {
            var UserKeyChain = ObjC.classes.UserKeyChain;
            if (UserKeyChain) {
                console.log("[+] 找到UserKeyChain类");
                
                // Hook getBdTtsAppId
                var originalGetBdTtsAppId = UserKeyChain['+ getBdTtsAppId'];
                if (originalGetBdTtsAppId) {
                    UserKeyChain['+ getBdTtsAppId'] = function() {
                        console.log("\n[*] ObjC Runtime: +[UserKeyChain getBdTtsAppId]");
                        console.log("[!] *** 固定返回AppId: " + fixedValues.appId + " ***");
                        return ObjC.classes.NSString.stringWithString_(fixedValues.appId);
                    };
                }
                
                // Hook getBdTtsApiKey
                var originalGetBdTtsApiKey = UserKeyChain['+ getBdTtsApiKey'];
                if (originalGetBdTtsApiKey) {
                    UserKeyChain['+ getBdTtsApiKey'] = function() {
                        console.log("\n[*] ObjC Runtime: +[UserKeyChain getBdTtsApiKey]");
                        console.log("[!] *** 固定返回ApiKey: " + fixedValues.apiKey + " ***");
                        return ObjC.classes.NSString.stringWithString_(fixedValues.apiKey);
                    };
                }
                
                // Hook getBdTtsSecretKey
                var originalGetBdTtsSecretKey = UserKeyChain['+ getBdTtsSecretKey'];
                if (originalGetBdTtsSecretKey) {
                    UserKeyChain['+ getBdTtsSecretKey'] = function() {
                        console.log("\n[*] ObjC Runtime: +[UserKeyChain getBdTtsSecretKey]");
                        console.log("[!] *** 固定返回SecretKey: " + fixedValues.secretKey + " ***");
                        return ObjC.classes.NSString.stringWithString_(fixedValues.secretKey);
                    };
                }
                
                // Hook getBdTtsSn
                var originalGetBdTtsSn = UserKeyChain['+ getBdTtsSn'];
                if (originalGetBdTtsSn) {
                    UserKeyChain['+ getBdTtsSn'] = function() {
                        console.log("\n[*] ObjC Runtime: +[UserKeyChain getBdTtsSn]");
                        console.log("[!] *** 固定返回SN: " + fixedValues.sn + " ***");
                        return ObjC.classes.NSString.stringWithString_(fixedValues.sn);
                    };
                }
                
                console.log("[+] ObjC运行时Hook设置完成");
                
            } else {
                console.log("[!] 未找到UserKeyChain类");
            }
        } catch (e) {
            console.log("[!] ObjC Runtime Hook失败: " + e.message);
        }
    }

    console.log("[*] TTS Hook设置完成!");
    console.log("[*] 已Hook的方法:");
    console.log("  1. UserKeyChain getBdTts* 系列方法 (固定返回值)");
    console.log("  2. BDSSpeechSynthesizer loadOfflineEngine (修改appCode参数，保持sn不变)");
    console.log("  3. BDSSpeechSynthesizer setApiKey (修改apiKey和secretKey参数)");
    console.log("[*] 固定返回值:");
    console.log("  AppId: " + fixedValues.appId);
    console.log("  ApiKey: " + fixedValues.apiKey);
    console.log("  SecretKey: " + fixedValues.secretKey);
    console.log("  SN: " + fixedValues.sn + " (仅用于UserKeyChain方法)");
    console.log("[*] 注意: loadOfflineEngine方法中的sn参数保持原始值不变");
    console.log("[*] 等待方法调用...");
}
